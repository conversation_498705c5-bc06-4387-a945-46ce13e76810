# Text Overlay Editing System

## Overview

The Text Overlay Editing System enables users to dynamically customize text overlay properties (font size, color, position, alignment, etc.) directly in the template preview interface. This system provides real-time visual feedback and maintains user customizations throughout the session.

## Architecture

### Core Components

#### 1. TextOverlayEditor Component
**Location**: `src/components/TextOverlayEditor/TextOverlayEditor.jsx`

Main editing interface with responsive design:
- **Desktop**: Fixed side panel (320px width) with collapse functionality
- **Mobile**: Full-screen modal with bottom sheet design
- **Features**: Overlay selection, property editing, real-time preview

#### 2. Property Editor Components
**Location**: `src/components/TextOverlayEditor/PropertyEditors/`

Specialized editors for different text properties:
- `FontSizeEditor.jsx` - Slider + input with presets (8-72px range)
- `ColorEditor.jsx` - Color picker with preset swatches and custom colors
- `AlignmentEditor.jsx` - Visual alignment buttons (left/center/right/justify)
- `StyleEditor.jsx` - Font weight, style, and decoration toggles
- `PositionEditor.jsx` - X/Y coordinates and width/height controls

#### 3. State Management Hook
**Location**: `src/hooks/useTextOverlayEditor.js`

Manages editing state with advanced features:
- Debounced preview updates (300ms default)
- Undo/redo functionality (10-level stack)
- Session storage persistence
- Error handling and validation

### Services

#### 1. Overlay Preview Service
**Location**: `src/services/overlayPreviewService.js`

Handles real-time preview generation:
- Debounced update queue management
- LRU cache with 50-entry limit
- Performance optimization for rapid changes
- Error recovery and fallback handling

#### 2. Enhanced Image Overlay Service
**Location**: `src/services/imageOverlayService.js`

Extended with customization support:
- `renderTemplateWithCustomizations()` method
- Overlay property merging logic
- Backward compatibility maintained

## Data Flow

```
User Input → Property Editor → useTextOverlayEditor Hook → Overlay Preview Service → Image Overlay Service → Canvas Rendering → Preview Update
```

### Customization Data Structure

```javascript
{
  templateId: "template-123",
  customizations: {
    "title": {
      styling: {
        fontSize: 42,
        color: "#FF0000",
        fontWeight: "bold"
      },
      position: {
        x: 60,
        y: 210
      }
    },
    "author": {
      styling: {
        color: "#333333",
        fontSize: 18
      }
    }
  }
}
```

## Integration Points

### 1. Cover Preview Interface
**Location**: `src/pages/document-template/components/CoverPreviewInterface.jsx`

Enhanced with editing capabilities:
- Text editor toggle button in header
- Real-time preview updates
- Customization indicators
- Undo/reset functionality

### 2. Template Workflow Hook
**Location**: `src/pages/document-template/hooks/useTemplateWorkflow.js`

Added `updateCoverPreviewWithCustomizations()` method:
- Integrates with overlay preview service
- Maintains workflow state consistency
- Error handling and logging

## Features

### Real-time Preview Updates
- 300ms debounced updates prevent performance issues
- Visual loading indicators during updates
- Error handling with user feedback
- Automatic retry on transient failures

### Session Persistence
- Automatic save every 5 seconds
- Recovery on page refresh
- Template-specific storage keys
- Graceful degradation if storage unavailable

### Mobile Optimization
- Touch-friendly controls (44px minimum)
- Swipe gestures for navigation
- Responsive layout adjustments
- iOS-specific optimizations (font-size: 16px to prevent zoom)

### Accessibility
- Keyboard navigation support
- High contrast mode compatibility
- Screen reader friendly labels
- Focus management and indicators

## Performance Optimizations

### 1. Debounced Updates
- Prevents excessive API calls during rapid changes
- Configurable delay (default 300ms)
- Queue management for multiple overlays

### 2. Preview Caching
- LRU cache with 50-entry limit
- Cache key generation based on template + customizations
- Selective cache invalidation
- Memory usage monitoring

### 3. Lazy Loading
- Property editors loaded on demand
- Background image caching
- Optimized canvas operations

## Testing

### Unit Tests
- Component rendering and interaction
- Hook state management
- Service functionality
- Error handling scenarios

### Integration Tests
- End-to-end editing workflow
- Preview update accuracy
- Session persistence
- Mobile responsiveness

### Test Files
- `src/components/TextOverlayEditor/__tests__/TextOverlayEditor.test.jsx`
- `src/hooks/__tests__/useTextOverlayEditor.test.js`
- `src/services/__tests__/overlayPreviewService.test.js`

## Usage Examples

### Basic Integration

```jsx
import TextOverlayEditor from '../components/TextOverlayEditor/TextOverlayEditor';
import useTextOverlayEditor from '../hooks/useTextOverlayEditor';

const MyComponent = ({ template, documentData }) => {
  const {
    customizations,
    handleCustomizationChange,
    handleReset
  } = useTextOverlayEditor(template, documentData, {
    onPreviewUpdate: handlePreviewUpdate
  });

  return (
    <TextOverlayEditor
      template={template}
      customizations={customizations}
      onCustomizationChange={handleCustomizationChange}
      onReset={handleReset}
      isVisible={true}
    />
  );
};
```

### Custom Preview Update Handler

```javascript
const handlePreviewUpdate = async (template, documentData, customizations) => {
  try {
    const updatedPreview = await coverPreviewService.generateCoverPreviewWithCustomizations(
      template,
      documentData,
      customizations
    );
    setPreviewData(updatedPreview);
  } catch (error) {
    console.error('Preview update failed:', error);
    setError(error.message);
  }
};
```

## Configuration Options

### useTextOverlayEditor Options

```javascript
{
  debounceMs: 300,           // Preview update delay
  enableSessionStorage: true, // Persist customizations
  onPreviewUpdate: null      // Preview update callback
}
```

### Overlay Preview Service Options

```javascript
{
  quality: 0.9,    // Image export quality
  format: 'png',   // Export format
  useCache: true   // Enable caching
}
```

## Browser Support

- **Modern Browsers**: Full functionality
- **IE11**: Basic functionality (no CSS Grid)
- **Mobile Safari**: Optimized touch interactions
- **Chrome Mobile**: Hardware acceleration enabled

## Future Enhancements

### Planned Features
1. Drag-and-drop positioning
2. Visual alignment guides
3. Font family selection
4. Text effects (shadow, outline)
5. Batch editing for multiple overlays
6. Custom preset management
7. Export/import customizations

### Performance Improvements
1. WebWorker for canvas operations
2. Virtual scrolling for large template lists
3. Progressive image loading
4. Memory usage optimization

## Troubleshooting

### Common Issues

1. **Preview not updating**: Check network connectivity and error console
2. **Customizations not persisting**: Verify session storage availability
3. **Mobile interface issues**: Check viewport meta tag and CSS media queries
4. **Performance problems**: Monitor cache size and debounce settings

### Debug Tools

```javascript
// Check cache statistics
console.log(overlayPreviewService.getCacheStats());

// Monitor customization changes
console.log(useTextOverlayEditor.getCustomizationSummary());

// Clear cache if needed
overlayPreviewService.clearCache();
```
