import React, { Suspense, lazy } from "react";
import { BrowserRouter, Routes as RouterRoutes, Route } from "react-router-dom";
import ScrollToTop from "components/ScrollToTop";
import ErrorBoundary from "components/ErrorBoundary";
import ProtectedRoute from "components/auth/ProtectedRoute";

// Lazy load page components for better code splitting
const Dashboard = lazy(() => import("pages/dashboard"));
const Projects = lazy(() => import("pages/projects"));
const AccountSettings = lazy(() => import("pages/account-settings"));
const DocumentCreator = lazy(() => import("pages/document-creator"));
const DocumentEditor = lazy(() => import("pages/document-editor"));
const DocumentTemplate = lazy(() => import("pages/document-template"));
const DocumentPublish = lazy(() => import("pages/document-editor/components/DocumentPublish"));
const DocumentReview = lazy(() => import("pages/document-editor/components/DocumentReview"));
const TemplateManager = lazy(() => import("pages/admin/TemplateManager"));
const AuthPage = lazy(() => import("pages/auth"));
const NotFound = lazy(() => import("pages/NotFound"));

// Loading component for Suspense fallback

// Loading component for Suspense fallback
const PageLoader = () => (
  <div className="flex items-center justify-center min-h-screen">
    <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
  </div>
);

const Routes = () => {
  return (
    <BrowserRouter>
      <ErrorBoundary>
        <ScrollToTop />
        <Suspense fallback={<PageLoader />}>
          <RouterRoutes>
        {/* Public routes */}
        <Route path="/auth" element={<AuthPage />} />

        {/* Protected routes */}
        <Route path="/" element={
          <ProtectedRoute>
            <Dashboard />
          </ProtectedRoute>
        } />
        <Route path="/dashboard" element={
          <ProtectedRoute>
            <Dashboard />
          </ProtectedRoute>
        } />
        <Route path="/documents" element={
          <ProtectedRoute>
            <Projects />
          </ProtectedRoute>
        } />
        <Route path="/account-settings" element={
          <ProtectedRoute>
            <AccountSettings />
          </ProtectedRoute>
        } />
        <Route path="/document-creator" element={
          <ProtectedRoute>
            <DocumentCreator />
          </ProtectedRoute>
        } />
        <Route path="/document-editor/:documentId" element={
          <ProtectedRoute>
            <DocumentEditor />
          </ProtectedRoute>
        } />
        <Route path="/document-template/:documentId" element={
          <ProtectedRoute>
            <DocumentTemplate />
          </ProtectedRoute>
        } />
        <Route path="/document-editor/:documentId/review" element={
          <ProtectedRoute>
            <DocumentReview />
          </ProtectedRoute>
        } />
        <Route path="/document-editor/:documentId/publish" element={
          <ProtectedRoute>
            <DocumentPublish />
          </ProtectedRoute>
        } />

        {/* Admin routes */}
        <Route path="/admin/templates" element={
          <ProtectedRoute>
            <TemplateManager />
          </ProtectedRoute>
        } />

        {/* Read-only document view */}

        {/* Fallback route */}
        <Route path="*" element={<NotFound />} />
          </RouterRoutes>
        </Suspense>
      </ErrorBoundary>
    </BrowserRouter>
  );
};

export default Routes;