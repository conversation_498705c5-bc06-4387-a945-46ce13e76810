import React, { useState, useCallback, useEffect } from 'react';
import { ChevronLeft, ChevronRight, RotateCcw, X, ChevronUp, ChevronDown } from 'lucide-react';
import FontSizeEditor from './PropertyEditors/FontSizeEditor';
import FontFamilyEditor from './PropertyEditors/FontFamilyEditor';
import ColorEditor from './PropertyEditors/ColorEditor';
import PositionEditor from './PropertyEditors/PositionEditor';
import AlignmentEditor from './PropertyEditors/AlignmentEditor';
import StyleEditor from './PropertyEditors/StyleEditor';

/**
 * Text Overlay Editor Component
 * Provides a side panel interface for editing text overlay properties
 * with real-time preview updates and mobile-responsive design
 */
const TextOverlayEditor = ({
  template = null,
  customizations = {},
  onCustomizationChange = null,
  onReset = null,
  isVisible = true,
  onToggleVisibility = null,
  selectedOverlayId: externalSelectedOverlayId = null,
  onOverlaySelect = null,
  className = ''
}) => {
  const [internalSelectedOverlayId, setInternalSelectedOverlayId] = useState(null);
  const [isCollapsed, setIsCollapsed] = useState(false);

  // Use external selection if provided, otherwise use internal state
  const selectedOverlayId = externalSelectedOverlayId || internalSelectedOverlayId;
  const setSelectedOverlayId = onOverlaySelect || setInternalSelectedOverlayId;

  // Get overlays from template
  const overlays = template?.text_overlays?.overlays || [];

  // Select first overlay by default if no external selection
  useEffect(() => {
    if (overlays.length > 0 && !selectedOverlayId && !externalSelectedOverlayId) {
      setSelectedOverlayId(overlays[0].id);
    }
  }, [overlays, selectedOverlayId, externalSelectedOverlayId, setSelectedOverlayId]);

  // Get current overlay data
  const selectedOverlay = overlays.find(overlay => overlay.id === selectedOverlayId);
  const overlayCustomizations = customizations[selectedOverlayId] || {};

  // Merge original styling with customizations
  const currentStyling = selectedOverlay ? {
    ...selectedOverlay.styling,
    ...overlayCustomizations.styling
  } : {};

  const currentPosition = selectedOverlay ? {
    ...selectedOverlay.position,
    ...overlayCustomizations.position
  } : {};

  // Handle property changes
  const handlePropertyChange = useCallback((property, value) => {
    if (!selectedOverlayId || !onCustomizationChange) return;

    const updatedCustomizations = {
      ...customizations,
      [selectedOverlayId]: {
        ...customizations[selectedOverlayId],
        [property]: {
          ...customizations[selectedOverlayId]?.[property],
          ...value
        }
      }
    };

    onCustomizationChange(updatedCustomizations);
  }, [selectedOverlayId, customizations, onCustomizationChange]);

  // Handle styling changes
  const handleStylingChange = useCallback((styleProperty, value) => {
    handlePropertyChange('styling', { [styleProperty]: value });
  }, [handlePropertyChange]);

  // Handle position changes
  const handlePositionChange = useCallback((positionProperty, value) => {
    handlePropertyChange('position', { [positionProperty]: value });
  }, [handlePropertyChange]);

  // Reset overlay to defaults
  const handleResetOverlay = useCallback(() => {
    if (!selectedOverlayId || !onCustomizationChange) return;

    const updatedCustomizations = { ...customizations };
    delete updatedCustomizations[selectedOverlayId];
    onCustomizationChange(updatedCustomizations);
  }, [selectedOverlayId, customizations, onCustomizationChange]);

  // Toggle panel visibility
  const handleToggleCollapse = useCallback(() => {
    setIsCollapsed(!isCollapsed);
  }, [isCollapsed]);

  if (!isVisible) return null;

  return (
    <div className={`text-overlay-editor ${className}`}>
      {/* Mobile: Adaptive bottom sheet overlay */}
      <div className="lg:hidden fixed inset-0 bg-black bg-opacity-50 z-50">
        <MobileEditorContent
          overlays={overlays}
          selectedOverlayId={selectedOverlayId}
          onSelectOverlay={setSelectedOverlayId}
          currentStyling={currentStyling}
          currentPosition={currentPosition}
          onStylingChange={handleStylingChange}
          onPositionChange={handlePositionChange}
          onReset={handleResetOverlay}
          onClose={() => onToggleVisibility?.(false)}
        />
      </div>

      {/* Desktop: Side panel */}
      <div className="hidden lg:block">
        <DesktopEditorPanel
          isCollapsed={isCollapsed}
          onToggleCollapse={handleToggleCollapse}
          overlays={overlays}
          selectedOverlayId={selectedOverlayId}
          onSelectOverlay={setSelectedOverlayId}
          currentStyling={currentStyling}
          currentPosition={currentPosition}
          onStylingChange={handleStylingChange}
          onPositionChange={handlePositionChange}
          onReset={handleResetOverlay}
          onResetAll={onReset}
        />
      </div>
    </div>
  );
};

/**
 * Mobile Editor Content Component with Adaptive Bottom Sheet
 */
const MobileEditorContent = ({
  overlays,
  selectedOverlayId,
  onSelectOverlay,
  currentStyling,
  currentPosition,
  onStylingChange,
  onPositionChange,
  onReset,
  onClose
}) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const [isDragging, setIsDragging] = useState(false);
  const [startY, setStartY] = useState(0);
  const [currentHeight, setCurrentHeight] = useState(() => {
    // Responsive initial height based on screen size and orientation
    const isLandscape = window.innerWidth > window.innerHeight;
    const isSmallScreen = window.innerHeight < 600;

    if (isLandscape) return 50; // More height in landscape
    if (isSmallScreen) return 45; // Slightly more on small screens
    return 40; // Default
  });

  // Handle drag start
  const handleDragStart = useCallback((e) => {
    setIsDragging(true);
    setStartY(e.touches ? e.touches[0].clientY : e.clientY);
  }, []);

  // Handle drag move
  const handleDragMove = useCallback((e) => {
    if (!isDragging) return;

    const currentY = e.touches ? e.touches[0].clientY : e.clientY;
    const deltaY = startY - currentY;
    const viewportHeight = window.innerHeight;
    const deltaVh = (deltaY / viewportHeight) * 100;

    // Calculate new height (between 25vh and 90vh)
    const newHeight = Math.min(90, Math.max(25, currentHeight + deltaVh));
    setCurrentHeight(newHeight);

    // Auto-expand/collapse based on height
    setIsExpanded(newHeight > 60);
  }, [isDragging, startY, currentHeight]);

  // Handle drag end
  const handleDragEnd = useCallback(() => {
    if (!isDragging) return;
    setIsDragging(false);

    // If dragged below 25vh, close the panel
    if (currentHeight < 25) {
      onClose();
      return;
    }

    // Snap to collapsed (40vh) or expanded (85vh) state
    if (currentHeight < 60) {
      setCurrentHeight(40);
      setIsExpanded(false);
    } else {
      setCurrentHeight(85);
      setIsExpanded(true);
    }
  }, [isDragging, currentHeight, onClose]);

  // Toggle expanded state with responsive heights
  const toggleExpanded = useCallback(() => {
    const newExpanded = !isExpanded;
    setIsExpanded(newExpanded);

    // Calculate responsive heights
    const isLandscape = window.innerWidth > window.innerHeight;
    const isSmallScreen = window.innerHeight < 600;

    if (newExpanded) {
      // Expanded state
      if (isLandscape) {
        setCurrentHeight(90); // More height in landscape
      } else if (isSmallScreen) {
        setCurrentHeight(88); // Almost full screen on small devices
      } else {
        setCurrentHeight(85); // Default expanded
      }
    } else {
      // Collapsed state
      if (isLandscape) {
        setCurrentHeight(50);
      } else if (isSmallScreen) {
        setCurrentHeight(45);
      } else {
        setCurrentHeight(40);
      }
    }
  }, [isExpanded]);

  // Handle keyboard navigation for drag handle
  const handleKeyDown = useCallback((e) => {
    if (e.key === 'Enter' || e.key === ' ') {
      e.preventDefault();
      toggleExpanded();
    } else if (e.key === 'Escape') {
      onClose();
    }
  }, [toggleExpanded, onClose]);

  // Add event listeners for drag and keyboard
  useEffect(() => {
    if (isDragging) {
      const handleMouseMove = (e) => handleDragMove(e);
      const handleMouseUp = () => handleDragEnd();
      const handleTouchMove = (e) => handleDragMove(e);
      const handleTouchEnd = () => handleDragEnd();

      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
      document.addEventListener('touchmove', handleTouchMove, { passive: false });
      document.addEventListener('touchend', handleTouchEnd);

      return () => {
        document.removeEventListener('mousemove', handleMouseMove);
        document.removeEventListener('mouseup', handleMouseUp);
        document.removeEventListener('touchmove', handleTouchMove);
        document.removeEventListener('touchend', handleTouchEnd);
      };
    }
  }, [isDragging, handleDragMove, handleDragEnd]);

  // Handle orientation and resize changes
  useEffect(() => {
    const handleResize = () => {
      // Update height based on new orientation/size
      const isLandscape = window.innerWidth > window.innerHeight;
      const isSmallScreen = window.innerHeight < 600;

      if (isExpanded) {
        if (isLandscape) {
          setCurrentHeight(90);
        } else if (isSmallScreen) {
          setCurrentHeight(88);
        } else {
          setCurrentHeight(85);
        }
      } else {
        if (isLandscape) {
          setCurrentHeight(50);
        } else if (isSmallScreen) {
          setCurrentHeight(45);
        } else {
          setCurrentHeight(40);
        }
      }
    };

    window.addEventListener('resize', handleResize);
    window.addEventListener('orientationchange', handleResize);

    return () => {
      window.removeEventListener('resize', handleResize);
      window.removeEventListener('orientationchange', handleResize);
    };
  }, [isExpanded]);

  return (
    <div
      className="mobile-bottom-sheet absolute bottom-0 left-0 right-0 bg-white rounded-t-xl shadow-2xl"
      style={{
        height: `${currentHeight}vh`,
        transform: isDragging ? 'none' : undefined,
        transition: isDragging ? 'none' : 'height 0.3s cubic-bezier(0.4, 0, 0.2, 1)'
      }}
    >
      <div className="flex flex-col h-full">
        {/* Drag Handle */}
        <div
          className="mobile-drag-handle flex items-center justify-center py-3 cursor-grab active:cursor-grabbing touch-pan-y focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-inset"
          onMouseDown={handleDragStart}
          onTouchStart={handleDragStart}
          onKeyDown={handleKeyDown}
          role="button"
          aria-label={`${isExpanded ? 'Collapse' : 'Expand'} text editor panel. Current height: ${Math.round(currentHeight)}% of screen`}
          aria-expanded={isExpanded}
          tabIndex={0}
        >
          <div className="w-12 h-1 bg-gray-300 rounded-full transition-colors"></div>
        </div>

        {/* Mobile Header */}
        <div className="mobile-editor-header flex items-center justify-between px-4 py-1 border-b border-gray-200">
          <div className="flex items-center space-x-2">
            <h3 className="text-lg font-semibold text-gray-900">Edit Text Style</h3>
            <button
              onClick={toggleExpanded}
              className="p-1 text-gray-400 hover:text-gray-600 transition-colors rounded-md"
              aria-label={isExpanded ? "Collapse panel" : "Expand panel"}
            >
              {isExpanded ? <ChevronDown className="w-4 h-4" /> : <ChevronUp className="w-4 h-4" />}
            </button>
          </div>
          <button
            onClick={onClose}
            className="p-2 text-gray-500 hover:text-gray-700 transition-colors rounded-lg hover:bg-gray-100"
            aria-label="Close editor"
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        {/* Compact Overlay Selection */}
        <div className="px-4 py-1 border-b border-gray-200 bg-gray-50">
          <CompactOverlaySelector
            overlays={overlays}
            selectedOverlayId={selectedOverlayId}
            onSelectOverlay={onSelectOverlay}
          />
        </div>

        {/* Scrollable Editor Content */}
        <div className="mobile-editor-content flex-1 overflow-y-auto">
          <div className="p-4">
            <EditorContent
              currentStyling={currentStyling}
              currentPosition={currentPosition}
              onStylingChange={onStylingChange}
              onPositionChange={onPositionChange}
              onReset={onReset}
              isMobile={true}
              isExpanded={isExpanded}
            />
          </div>
        </div>
      </div>
    </div>
  );
};

/**
 * Compact Overlay Selector Component for Mobile
 */
const CompactOverlaySelector = ({ overlays, selectedOverlayId, onSelectOverlay }) => {
  if (overlays.length === 0) return null;

  if (overlays.length === 1) {
    // Single overlay - just show the name with better styling
    return (
      <div className="compact-overlay-selector">
        <div className="text-sm text-gray-600">
          Editing: <span className="font-semibold text-gray-900 capitalize">{overlays[0].id}</span>
        </div>
      </div>
    );
  }

  // Multiple overlays - show compact dropdown
  return (
    <div className="compact-overlay-selector">
      <div className="flex items-center space-x-2">
        <span className="text-sm text-gray-600 whitespace-nowrap">Editing:</span>
        <select
          value={selectedOverlayId || ''}
          onChange={(e) => onSelectOverlay(e.target.value)}
          className="flex-1 text-sm bg-transparent border-none focus:ring-0 focus:outline-none font-semibold text-gray-900 capitalize cursor-pointer"
        >
          {overlays.map((overlay) => (
            <option key={overlay.id} value={overlay.id} className="capitalize">
              {overlay.id}
            </option>
          ))}
        </select>
        <ChevronDown className="w-4 h-4 text-gray-400 pointer-events-none" />
      </div>
    </div>
  );
};

/**
 * Desktop Editor Panel Component
 */
const DesktopEditorPanel = ({
  isCollapsed,
  onToggleCollapse,
  overlays,
  selectedOverlayId,
  onSelectOverlay,
  currentStyling,
  currentPosition,
  onStylingChange,
  onPositionChange,
  onReset,
  onResetAll
}) => {
  const panelWidth = isCollapsed ? '48px' : '320px';

  return (
    <div
      className="fixed right-0 top-0 h-full bg-white border-l border-gray-200 shadow-lg transition-all duration-300 z-40"
      style={{ width: panelWidth }}
    >
      {/* Collapse Toggle */}
      <button
        onClick={onToggleCollapse}
        className="absolute left-0 top-1/2 transform -translate-y-1/2 -translate-x-full bg-white border border-gray-200 rounded-l-lg p-2 shadow-md hover:bg-gray-50 transition-colors"
      >
        {isCollapsed ? (
          <ChevronLeft className="w-4 h-4 text-gray-600" />
        ) : (
          <ChevronRight className="w-4 h-4 text-gray-600" />
        )}
      </button>

      {!isCollapsed && (
        <div className="flex flex-col h-full">
          {/* Panel Header */}
          <div className="p-4 border-b border-gray-200">
            <div className="flex items-center justify-between mb-3">
              <h3 className="text-lg font-semibold text-gray-900">Edit Text Style</h3>
              <button
                onClick={onResetAll}
                className="p-2 text-gray-500 hover:text-gray-700 transition-colors"
                title="Reset all changes"
              >
                <RotateCcw className="w-4 h-4" />
              </button>
            </div>

            {/* Overlay Selection */}
            <OverlaySelector
              overlays={overlays}
              selectedOverlayId={selectedOverlayId}
              onSelectOverlay={onSelectOverlay}
              isMobile={false}
            />
          </div>

          {/* Editor Content */}
          <div className="flex-1 overflow-y-auto p-4">
            <EditorContent
              currentStyling={currentStyling}
              currentPosition={currentPosition}
              onStylingChange={onStylingChange}
              onPositionChange={onPositionChange}
              onReset={onReset}
              isMobile={false}
            />
          </div>
        </div>
      )}
    </div>
  );
};

/**
 * Overlay Selector Component
 */
const OverlaySelector = ({ overlays, selectedOverlayId, onSelectOverlay, isMobile }) => {
  if (overlays.length === 0) return null;

  return (
    <div className="space-y-2">
      <label className="block text-sm font-medium text-gray-700">
        Select Text Element
      </label>
      <div className={`grid gap-2 ${isMobile ? 'grid-cols-1' : 'grid-cols-1'}`}>
        {overlays.map((overlay) => (
          <button
            key={overlay.id}
            onClick={() => onSelectOverlay(overlay.id)}
            className={`p-3 text-left rounded-lg border transition-colors ${
              selectedOverlayId === overlay.id
                ? 'border-blue-500 bg-blue-50 text-blue-900'
                : 'border-gray-200 bg-white text-gray-700 hover:border-gray-300'
            }`}
          >
            <div className="font-medium capitalize">{overlay.id}</div>
            <div className="text-sm text-gray-500 truncate">
              {overlay.placeholder}
            </div>
          </button>
        ))}
      </div>
    </div>
  );
};

/**
 * Editor Content Component
 */
const EditorContent = ({
  currentStyling,
  currentPosition,
  onStylingChange,
  onPositionChange,
  onReset,
  isMobile,
  isExpanded = true
}) => {
  // Priority controls (always visible)
  const priorityControls = (
    <>
      {/* Font Size - Most important for mobile */}
      <FontSizeEditor
        value={currentStyling.fontSize || 16}
        onChange={(value) => onStylingChange('fontSize', value)}
        isMobile={isMobile}
      />

      {/* Color - Second most important */}
      <ColorEditor
        value={currentStyling.color || '#000000'}
        onChange={(value) => onStylingChange('color', value)}
        isMobile={isMobile}
      />

      {/* Text Alignment - Third priority */}
      <AlignmentEditor
        value={currentStyling.textAlign || 'left'}
        onChange={(value) => onStylingChange('textAlign', value)}
        isMobile={isMobile}
      />
    </>
  );

  // Advanced controls (shown when expanded or on desktop)
  const advancedControls = (
    <>
      {/* Font Family */}
      <FontFamilyEditor
        value={currentStyling.fontFamily || 'Arial'}
        onChange={(value) => onStylingChange('fontFamily', value)}
        isMobile={isMobile}
      />

      {/* Font Style */}
      <StyleEditor
        fontWeight={currentStyling.fontWeight || 'normal'}
        fontStyle={currentStyling.fontStyle || 'normal'}
        onFontWeightChange={(value) => onStylingChange('fontWeight', value)}
        onFontStyleChange={(value) => onStylingChange('fontStyle', value)}
        isMobile={isMobile}
      />

      {/* Position */}
      <PositionEditor
        position={currentPosition}
        onChange={onPositionChange}
        isMobile={isMobile}
      />

      {/* Reset Button */}
      <div className="pt-4 border-t border-gray-200">
        <button
          onClick={onReset}
          className="w-full px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
        >
          Reset to Default
        </button>
      </div>
    </>
  );

  return (
    <div className={`space-y-${isMobile ? '4' : '6'}`}>
      {/* Always show priority controls */}
      {priorityControls}

      {/* Show advanced controls based on expansion state */}
      {(!isMobile || isExpanded) && (
        <>
          {/* Separator for mobile when showing advanced controls */}
          {isMobile && (
            <div className="mobile-advanced-separator">
              <span>Advanced Options</span>
            </div>
          )}
          {advancedControls}
        </>
      )}

      {/* Show expand hint on mobile when collapsed */}
      {isMobile && !isExpanded && (
        <div className="text-center py-3">
          <div className="mobile-expand-hint text-xs text-gray-500 flex items-center justify-center space-x-1">
            <ChevronUp className="w-3 h-3" />
            <span>Pull up for more options</span>
          </div>
        </div>
      )}
    </div>
  );
};

export default TextOverlayEditor;
