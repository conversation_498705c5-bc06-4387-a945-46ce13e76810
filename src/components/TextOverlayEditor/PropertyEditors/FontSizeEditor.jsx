import React, { useState, useCallback, useEffect } from 'react';
import { Type } from 'lucide-react';

/**
 * Font Size Editor Component
 * Provides direct input control for adjusting font size
 * with real-time preview and mobile-optimized interface
 */
const FontSizeEditor = ({
  value = 16,
  onChange = null,
  min = 1,
  step = 1,
  isMobile = false,
  className = ''
}) => {
  const [inputValue, setInputValue] = useState(value.toString());

  // Update input when value prop changes
  useEffect(() => {
    setInputValue(value.toString());
  }, [value]);

  // Handle input change
  const handleInputChange = useCallback((event) => {
    const newValue = event.target.value;
    setInputValue(newValue);

    // Validate and apply if valid (only check minimum, no maximum limit)
    const numValue = parseInt(newValue, 10);
    if (!isNaN(numValue) && numValue >= min) {
      onChange?.(numValue);
    }
  }, [onChange, min]);

  // Handle input blur (apply value if valid)
  const handleInputBlur = useCallback(() => {
    const numValue = parseInt(inputValue, 10);
    if (isNaN(numValue)) {
      setInputValue(value.toString());
      return;
    }

    // Only enforce minimum value, no maximum limit
    const clampedValue = Math.max(min, numValue);
    setInputValue(clampedValue.toString());

    if (clampedValue !== value) {
      onChange?.(clampedValue);
    }
  }, [inputValue, value, onChange, min]);

  // Quick size presets (updated with larger sizes)
  const presets = [
    { label: 'Small', value: 14 },
    { label: 'Medium', value: 24 },
    { label: 'Large', value: 36 },
    { label: 'XL', value: 48 },
    { label: 'XXL', value: 72 },
    { label: 'Huge', value: 96 }
  ];

  const handlePresetClick = useCallback((presetValue) => {
    setInputValue(presetValue.toString());
    onChange?.(presetValue);
  }, [onChange]);

  return (
    <div className={`font-size-editor ${className}`}>
      {/* Header */}
      <div className="flex items-center space-x-2 mb-3">
        <Type className="w-4 h-4 text-gray-600" />
        <label className="block text-sm font-medium text-gray-700">
          Font Size
        </label>
        <span className="text-xs text-gray-500">({value}px)</span>
      </div>



      {/* Direct Input */}
      <div className="mb-4">
        <div className="relative">
          <input
            type="number"
            min={min}
            value={inputValue}
            onChange={handleInputChange}
            onBlur={handleInputBlur}
            className={`w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
              isMobile ? 'text-base' : 'text-sm'
            }`}
            placeholder="Enter font size"
          />
          <span className="absolute right-3 top-1/2 transform -translate-y-1/2 text-xs text-gray-500">
            px
          </span>
        </div>
        <p className="text-xs text-gray-500 mt-1">
          Enter any size (minimum {min}px, no maximum limit)
        </p>
      </div>

      {/* Quick Size Presets */}
      <div className="mb-3">
        <label className="block text-xs text-gray-600 mb-2">Quick Sizes:</label>
        <div className="flex flex-wrap gap-1">
          {presets.map((preset) => (
            <button
              key={preset.value}
              onClick={() => handlePresetClick(preset.value)}
              className={`px-2 py-1 text-xs border rounded transition-colors ${
                value === preset.value
                  ? 'bg-blue-100 border-blue-300 text-blue-700'
                  : 'bg-gray-50 border-gray-300 text-gray-600 hover:bg-gray-100'
              } ${isMobile ? 'px-3 py-2' : 'px-2 py-1'}`}
            >
              {preset.label} ({preset.value}px)
            </button>
          ))}
        </div>
      </div>
    </div>
  );
};

export default FontSizeEditor;
