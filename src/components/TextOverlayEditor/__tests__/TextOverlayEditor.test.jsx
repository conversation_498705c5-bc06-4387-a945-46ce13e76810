import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import TextOverlayEditor from '../TextOverlayEditor';

// Mock the property editor components
jest.mock('../PropertyEditors/FontSizeEditor', () => {
  return function MockFontSizeEditor({ value, onChange }) {
    return (
      <div data-testid="font-size-editor">
        <input
          data-testid="font-size-input"
          type="number"
          value={value}
          onChange={(e) => onChange(parseInt(e.target.value, 10))}
        />
      </div>
    );
  };
});

jest.mock('../PropertyEditors/ColorEditor', () => {
  return function MockColorEditor({ value, onChange }) {
    return (
      <div data-testid="color-editor">
        <input
          data-testid="color-input"
          type="color"
          value={value}
          onChange={(e) => onChange(e.target.value)}
        />
      </div>
    );
  };
});

jest.mock('../PropertyEditors/AlignmentEditor', () => {
  return function MockAlignmentEditor({ value, onChange }) {
    return (
      <div data-testid="alignment-editor">
        <select
          data-testid="alignment-select"
          value={value}
          onChange={(e) => onChange(e.target.value)}
        >
          <option value="left">Left</option>
          <option value="center">Center</option>
          <option value="right">Right</option>
        </select>
      </div>
    );
  };
});

jest.mock('../PropertyEditors/StyleEditor', () => {
  return function MockStyleEditor({ fontWeight, onFontWeightChange }) {
    return (
      <div data-testid="style-editor">
        <button
          data-testid="bold-toggle"
          onClick={() => onFontWeightChange(fontWeight === 'bold' ? 'normal' : 'bold')}
        >
          {fontWeight === 'bold' ? 'Unbold' : 'Bold'}
        </button>
      </div>
    );
  };
});

jest.mock('../PropertyEditors/PositionEditor', () => {
  return function MockPositionEditor({ position, onChange }) {
    return (
      <div data-testid="position-editor">
        <input
          data-testid="x-input"
          type="number"
          value={position.x}
          onChange={(e) => onChange('x', parseInt(e.target.value, 10))}
        />
        <input
          data-testid="y-input"
          type="number"
          value={position.y}
          onChange={(e) => onChange('y', parseInt(e.target.value, 10))}
        />
      </div>
    );
  };
});

describe('TextOverlayEditor', () => {
  const mockTemplate = {
    id: 'test-template',
    text_overlays: {
      overlays: [
        {
          id: 'title',
          placeholder: '{{title}}',
          position: { x: 50, y: 100, width: 400, height: 60 },
          styling: {
            fontSize: 32,
            fontFamily: 'Arial',
            fontWeight: 'bold',
            color: '#000000',
            textAlign: 'center'
          }
        },
        {
          id: 'author',
          placeholder: 'by {{author}}',
          position: { x: 50, y: 200, width: 400, height: 30 },
          styling: {
            fontSize: 18,
            fontFamily: 'Arial',
            fontWeight: 'normal',
            color: '#666666',
            textAlign: 'center'
          }
        }
      ]
    }
  };

  const mockCustomizations = {
    title: {
      styling: {
        fontSize: 40,
        color: '#FF0000'
      }
    }
  };

  const defaultProps = {
    template: mockTemplate,
    customizations: {},
    onCustomizationChange: jest.fn(),
    onReset: jest.fn(),
    isVisible: true,
    onToggleVisibility: jest.fn()
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('renders without crashing', () => {
    render(<TextOverlayEditor {...defaultProps} />);
    expect(screen.getByText('Edit Text Style')).toBeInTheDocument();
  });

  test('displays overlay selection options', () => {
    render(<TextOverlayEditor {...defaultProps} />);
    
    expect(screen.getByText('title')).toBeInTheDocument();
    expect(screen.getByText('author')).toBeInTheDocument();
  });

  test('selects first overlay by default', () => {
    render(<TextOverlayEditor {...defaultProps} />);
    
    // The title overlay should be selected by default
    const titleButton = screen.getByRole('button', { name: /title/i });
    expect(titleButton).toHaveClass('border-blue-500');
  });

  test('switches between overlays', () => {
    render(<TextOverlayEditor {...defaultProps} />);
    
    // Click on author overlay
    const authorButton = screen.getByRole('button', { name: /author/i });
    fireEvent.click(authorButton);
    
    // Author should now be selected
    expect(authorButton).toHaveClass('border-blue-500');
  });

  test('displays property editors for selected overlay', () => {
    render(<TextOverlayEditor {...defaultProps} />);
    
    // Should show all property editors
    expect(screen.getByTestId('font-size-editor')).toBeInTheDocument();
    expect(screen.getByTestId('color-editor')).toBeInTheDocument();
    expect(screen.getByTestId('alignment-editor')).toBeInTheDocument();
    expect(screen.getByTestId('style-editor')).toBeInTheDocument();
    expect(screen.getByTestId('position-editor')).toBeInTheDocument();
  });

  test('calls onCustomizationChange when font size changes', () => {
    const onCustomizationChange = jest.fn();
    render(<TextOverlayEditor {...defaultProps} onCustomizationChange={onCustomizationChange} />);
    
    const fontSizeInput = screen.getByTestId('font-size-input');
    fireEvent.change(fontSizeInput, { target: { value: '48' } });
    
    expect(onCustomizationChange).toHaveBeenCalledWith({
      title: {
        styling: {
          fontSize: 48
        }
      }
    });
  });

  test('calls onCustomizationChange when color changes', () => {
    const onCustomizationChange = jest.fn();
    render(<TextOverlayEditor {...defaultProps} onCustomizationChange={onCustomizationChange} />);
    
    const colorInput = screen.getByTestId('color-input');
    fireEvent.change(colorInput, { target: { value: '#FF0000' } });
    
    expect(onCustomizationChange).toHaveBeenCalledWith({
      title: {
        styling: {
          color: '#FF0000'
        }
      }
    });
  });

  test('calls onCustomizationChange when position changes', () => {
    const onCustomizationChange = jest.fn();
    render(<TextOverlayEditor {...defaultProps} onCustomizationChange={onCustomizationChange} />);
    
    const xInput = screen.getByTestId('x-input');
    fireEvent.change(xInput, { target: { value: '100' } });
    
    expect(onCustomizationChange).toHaveBeenCalledWith({
      title: {
        position: {
          x: 100
        }
      }
    });
  });

  test('displays current customizations', () => {
    render(<TextOverlayEditor {...defaultProps} customizations={mockCustomizations} />);
    
    // Font size should show customized value
    const fontSizeInput = screen.getByTestId('font-size-input');
    expect(fontSizeInput.value).toBe('40');
    
    // Color should show customized value
    const colorInput = screen.getByTestId('color-input');
    expect(colorInput.value).toBe('#FF0000');
  });

  test('resets overlay customizations', () => {
    const onCustomizationChange = jest.fn();
    render(<TextOverlayEditor {...defaultProps} 
      customizations={mockCustomizations}
      onCustomizationChange={onCustomizationChange}
    />);
    
    const resetButton = screen.getByText('Reset to Default');
    fireEvent.click(resetButton);
    
    expect(onCustomizationChange).toHaveBeenCalledWith({});
  });

  test('handles template without overlays', () => {
    const templateWithoutOverlays = {
      id: 'empty-template',
      text_overlays: {
        overlays: []
      }
    };
    
    render(<TextOverlayEditor {...defaultProps} template={templateWithoutOverlays} />);
    
    // Should still render the editor but without overlay options
    expect(screen.getByText('Edit Text Style')).toBeInTheDocument();
  });

  test('handles null template', () => {
    render(<TextOverlayEditor {...defaultProps} template={null} />);
    
    // Should render without crashing
    expect(screen.getByText('Edit Text Style')).toBeInTheDocument();
  });

  test('does not render when not visible', () => {
    render(<TextOverlayEditor {...defaultProps} isVisible={false} />);
    
    // Should not render anything
    expect(screen.queryByText('Edit Text Style')).not.toBeInTheDocument();
  });

  test('merges original styling with customizations', () => {
    render(<TextOverlayEditor {...defaultProps} customizations={mockCustomizations} />);
    
    // Font size should be customized value
    const fontSizeInput = screen.getByTestId('font-size-input');
    expect(fontSizeInput.value).toBe('40');
    
    // Text alignment should be original value (not customized)
    const alignmentSelect = screen.getByTestId('alignment-select');
    expect(alignmentSelect.value).toBe('center');
  });
});

describe('TextOverlayEditor Mobile Behavior', () => {
  const defaultProps = {
    template: {
      id: 'test-template',
      text_overlays: {
        overlays: [
          {
            id: 'title',
            placeholder: '{{title}}',
            position: { x: 50, y: 100, width: 400, height: 60 },
            styling: {
              fontSize: 32,
              color: '#000000',
              textAlign: 'center'
            }
          }
        ]
      }
    },
    customizations: {},
    onCustomizationChange: jest.fn(),
    onReset: jest.fn(),
    isVisible: true,
    onToggleVisibility: jest.fn()
  };

  test('renders mobile interface on small screens', () => {
    // Mock window.innerWidth for mobile
    Object.defineProperty(window, 'innerWidth', {
      writable: true,
      configurable: true,
      value: 500,
    });

    render(<TextOverlayEditor {...defaultProps} />);
    
    // Should render mobile interface
    expect(screen.getByText('Edit Text Style')).toBeInTheDocument();
  });
});
