/* Text Overlay Editor Styles */

/* Custom slider styling for font size editor */
.slider {
  -webkit-appearance: none;
  appearance: none;
  outline: none;
  opacity: 0.7;
  transition: opacity 0.2s;
}

.slider:hover {
  opacity: 1;
}

.slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: #3B82F6;
  cursor: pointer;
  border: 2px solid #ffffff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.slider::-moz-range-thumb {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: #3B82F6;
  cursor: pointer;
  border: 2px solid #ffffff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Mobile optimizations for sliders */
@media (max-width: 768px) {
  .slider::-webkit-slider-thumb {
    width: 24px;
    height: 24px;
  }
  
  .slider::-moz-range-thumb {
    width: 24px;
    height: 24px;
  }
}

/* Text overlay editor panel animations */
.text-overlay-editor {
  transition: all 0.3s ease-in-out;
}

/* Color picker custom styling */
.color-picker-swatch {
  transition: transform 0.2s ease;
}

.color-picker-swatch:hover {
  transform: scale(1.1);
}

.color-picker-swatch:active {
  transform: scale(0.95);
}

/* Position editor directional controls */
.position-controls {
  user-select: none;
}

.position-controls button {
  transition: all 0.2s ease;
}

.position-controls button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.position-controls button:active {
  transform: translateY(0);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Style editor toggle buttons */
.style-toggle {
  transition: all 0.2s ease;
}

.style-toggle.active {
  transform: scale(1.05);
}

/* Alignment editor buttons */
.alignment-button {
  transition: all 0.2s ease;
}

.alignment-button:hover {
  transform: translateY(-1px);
}

.alignment-button.selected {
  box-shadow: 0 0 0 2px #3B82F6;
}

/* Mobile editor modal animations */
@media (max-width: 1024px) {
  .mobile-editor-modal {
    animation: slideUp 0.3s ease-out;
  }
  
  @keyframes slideUp {
    from {
      transform: translateY(100%);
      opacity: 0;
    }
    to {
      transform: translateY(0);
      opacity: 1;
    }
  }
}

/* Desktop editor panel animations */
@media (min-width: 1024px) {
  .desktop-editor-panel {
    animation: slideLeft 0.3s ease-out;
  }
  
  @keyframes slideLeft {
    from {
      transform: translateX(100%);
      opacity: 0;
    }
    to {
      transform: translateX(0);
      opacity: 1;
    }
  }
}

/* Preview updating indicator */
.preview-updating {
  position: relative;
  overflow: hidden;
}

.preview-updating::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 2px;
  background: linear-gradient(90deg, transparent, #3B82F6, transparent);
  animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}

/* Customization indicator dots */
.customization-dot {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* Smooth transitions for all interactive elements */
.text-overlay-editor button,
.text-overlay-editor input,
.text-overlay-editor select {
  transition: all 0.2s ease;
}

/* Focus states for accessibility */
.text-overlay-editor button:focus,
.text-overlay-editor input:focus,
.text-overlay-editor select:focus {
  outline: 2px solid #3B82F6;
  outline-offset: 2px;
}

/* Disabled state styling */
.text-overlay-editor button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none !important;
}

/* Loading state for buttons */
.button-loading {
  position: relative;
  color: transparent !important;
}

.button-loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 16px;
  height: 16px;
  margin: -8px 0 0 -8px;
  border: 2px solid #ffffff;
  border-radius: 50%;
  border-top-color: transparent;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Error state styling */
.editor-error {
  animation: shake 0.5s ease-in-out;
}

@keyframes shake {
  0%, 100% {
    transform: translateX(0);
  }
  25% {
    transform: translateX(-5px);
  }
  75% {
    transform: translateX(5px);
  }
}

/* Success state styling */
.editor-success {
  animation: bounce 0.6s ease-in-out;
}

@keyframes bounce {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-5px);
  }
}

/* Responsive text sizing */
@media (max-width: 640px) {
  .text-overlay-editor {
    font-size: 14px;
  }
  
  .text-overlay-editor h3 {
    font-size: 16px;
  }
  
  .text-overlay-editor button {
    min-height: 44px; /* Touch-friendly minimum */
  }
  
  .text-overlay-editor input {
    min-height: 44px;
    font-size: 16px; /* Prevent zoom on iOS */
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .text-overlay-editor {
    border-width: 2px;
  }
  
  .text-overlay-editor button {
    border-width: 2px;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .text-overlay-editor *,
  .text-overlay-editor *::before,
  .text-overlay-editor *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}
